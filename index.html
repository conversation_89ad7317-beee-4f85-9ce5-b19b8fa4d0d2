<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Rubik's Cube - ML Training Data Generator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>3D Rubik's Cube - ML Training Data Generator</h1>
            <p>Generate training data for machine learning models</p>
        </header>

        <div class="main-content">
            <!-- 3D Canvas Container -->
            <div class="canvas-container">
                <canvas id="cube-canvas"></canvas>
                <div class="canvas-overlay">
                    <div class="camera-info">
                        <span id="camera-position">Camera: Default</span>
                    </div>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="control-panel">
                <div class="control-section">
                    <h3>Cube Controls</h3>
                    <div class="button-group">
                        <button id="scramble-btn" class="btn btn-primary">Scramble</button>
                        <button id="reset-btn" class="btn btn-secondary">Reset</button>
                    </div>
                </div>

                <div class="control-section">
                    <h3>Manual Rotations</h3>
                    <div class="rotation-controls">
                        <div class="rotation-row">
                            <button class="btn btn-rotation" data-move="R">R</button>
                            <button class="btn btn-rotation" data-move="L">L</button>
                            <button class="btn btn-rotation" data-move="U">U</button>
                        </div>
                        <div class="rotation-row">
                            <button class="btn btn-rotation" data-move="D">D</button>
                            <button class="btn btn-rotation" data-move="F">F</button>
                            <button class="btn btn-rotation" data-move="B">B</button>
                        </div>
                        <div class="rotation-row">
                            <button class="btn btn-rotation" data-move="R'">R'</button>
                            <button class="btn btn-rotation" data-move="L'">L'</button>
                            <button class="btn btn-rotation" data-move="U'">U'</button>
                        </div>
                        <div class="rotation-row">
                            <button class="btn btn-rotation" data-move="D'">D'</button>
                            <button class="btn btn-rotation" data-move="F'">F'</button>
                            <button class="btn btn-rotation" data-move="B'">B'</button>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3>Camera Presets</h3>
                    <div class="button-group">
                        <button id="camera-front" class="btn btn-camera">Front</button>
                        <button id="camera-corner" class="btn btn-camera">Corner</button>
                        <button id="camera-top" class="btn btn-camera">Top</button>
                        <button id="camera-side" class="btn btn-camera">Side</button>
                    </div>
                </div>

                <div class="control-section">
                    <h3>Data Generation</h3>
                    <div class="data-controls">
                        <div class="input-group">
                            <label for="batch-size">Batch Size:</label>
                            <input type="number" id="batch-size" value="10" min="1" max="100">
                        </div>
                        <div class="input-group">
                            <label for="angles-per-state">Angles per State:</label>
                            <input type="number" id="angles-per-state" value="4" min="1" max="8">
                        </div>
                        <div class="button-group">
                            <button id="capture-state" class="btn btn-success">Capture Current State</button>
                            <button id="generate-batch" class="btn btn-primary">Generate Batch</button>
                            <button id="export-data" class="btn btn-warning">Export Data</button>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3>Current State Info</h3>
                    <div class="state-info">
                        <div class="info-item">
                            <span class="label">Moves from Solved:</span>
                            <span id="moves-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Current Camera:</span>
                            <span id="current-camera">Default</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Captured States:</span>
                            <span id="captured-count">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <span id="status-text">Ready</span>
            </div>
            <div class="status-item">
                <span id="performance-info">FPS: --</span>
            </div>
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- OrbitControls for camera interaction -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <!-- Main Application -->
    <script src="main.js"></script>
</body>
</html>
