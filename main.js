// 3D Rubik's Cube - ML Training Data Generator
// Main Application Class

class RubiksCube {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.cube = null;
        this.cubelets = [];
        this.isAnimating = false;
        this.moveHistory = [];
        this.capturedStates = [];
        this.wireframeMode = false;

        // Cube colors (standard Rubik's cube colors)
        this.colors = {
            white: 0xffffff,   // Top
            yellow: 0xffff00,  // Bottom
            red: 0xff0000,     // Right
            orange: 0xff8800,  // Left
            blue: 0x0000ff,    // Front
            green: 0x00ff00    // Back
        };
        
        // Camera presets
        this.cameraPresets = {
            front: { position: [0, 0, 8], target: [0, 0, 0] },
            corner: { position: [5, 5, 5], target: [0, 0, 0] },
            top: { position: [0, 8, 0], target: [0, 0, 0] },
            side: { position: [8, 0, 0], target: [0, 0, 0] }
        };
        
        this.init();
        this.setupEventListeners();
        this.animate();
    }
    
    init() {
        // Get canvas element
        const canvas = document.getElementById('cube-canvas');
        
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);
        
        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            canvas.clientWidth / canvas.clientHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(5, 5, 5);
        
        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas, 
            antialias: true,
            preserveDrawingBuffer: true // For screenshots
        });
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Add lights
        this.setupLighting();
        
        // Create the Rubik's cube
        this.createCube();
        
        // Setup camera controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.target.set(0, 0, 0);

        // Configure stepwise zoom
        this.controls.enableZoom = false; // Disable default zoom
        this.setupCustomZoom();
        
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    setupCustomZoom() {
        const canvas = this.renderer.domElement;

        canvas.addEventListener('wheel', (event) => {
            event.preventDefault();

            if (event.deltaY < 0) {
                this.zoomIn(); // Scroll up = zoom in
            } else {
                this.zoomOut(); // Scroll down = zoom out
            }
        }, { passive: false });
    }
    
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        
        // Additional fill light
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-5, -5, -5);
        this.scene.add(fillLight);
    }
    
    createCube() {
        this.cube = new THREE.Group();
        this.cubelets = [];
        
        const size = 0.95; // Slightly smaller than 1 to show gaps
        const positions = [-1, 0, 1]; // 3x3x3 positions
        
        positions.forEach((x, xi) => {
            positions.forEach((y, yi) => {
                positions.forEach((z, zi) => {
                    const cubelet = this.createCubelet(size);
                    cubelet.position.set(x, y, z);
                    
                    // Store position indices for state tracking
                    cubelet.userData = { x: xi, y: yi, z: zi };
                    
                    this.cube.add(cubelet);
                    this.cubelets.push(cubelet);
                });
            });
        });
        
        this.scene.add(this.cube);
        this.applySolvedColors();
    }
    
    createCubelet(size) {
        const geometry = new THREE.BoxGeometry(size, size, size);

        // Create materials for each face with slight shininess
        const materials = [
            new THREE.MeshPhongMaterial({ color: 0x333333, shininess: 30 }), // Right
            new THREE.MeshPhongMaterial({ color: 0x333333, shininess: 30 }), // Left
            new THREE.MeshPhongMaterial({ color: 0x333333, shininess: 30 }), // Top
            new THREE.MeshPhongMaterial({ color: 0x333333, shininess: 30 }), // Bottom
            new THREE.MeshPhongMaterial({ color: 0x333333, shininess: 30 }), // Front
            new THREE.MeshPhongMaterial({ color: 0x333333, shininess: 30 })  // Back
        ];

        const cubelet = new THREE.Mesh(geometry, materials);
        cubelet.castShadow = true;
        cubelet.receiveShadow = true;

        // Add black edges for more realistic look
        const edges = new THREE.EdgesGeometry(geometry);
        const edgeMaterial = new THREE.LineBasicMaterial({ color: 0x000000, linewidth: 3 });
        const edgeLines = new THREE.LineSegments(edges, edgeMaterial);
        cubelet.add(edgeLines);

        // Store edge lines for wireframe mode
        cubelet.userData.edgeLines = edgeLines;

        return cubelet;
    }
    
    applySolvedColors() {
        this.cubelets.forEach(cubelet => {
            const { x, y, z } = cubelet.userData;
            const materials = cubelet.material;

            // Reset all faces to dark first
            materials.forEach(mat => mat.color.setHex(0x333333));

            // Apply colors based on position - Standard orientation: Green front, White top
            // Right face (x = 2) - Orange
            if (x === 2) materials[0].color.setHex(this.colors.orange);
            // Left face (x = 0) - Red
            if (x === 0) materials[1].color.setHex(this.colors.red);
            // Top face (y = 2) - White
            if (y === 2) materials[2].color.setHex(this.colors.white);
            // Bottom face (y = 0) - Yellow
            if (y === 0) materials[3].color.setHex(this.colors.yellow);
            // Front face (z = 2) - Green
            if (z === 2) materials[4].color.setHex(this.colors.green);
            // Back face (z = 0) - Blue
            if (z === 0) materials[5].color.setHex(this.colors.blue);
        });
    }
    
    setupEventListeners() {
        // Cube control buttons
        document.getElementById('scramble-btn').addEventListener('click', () => this.scramble());
        document.getElementById('reset-btn').addEventListener('click', () => this.reset());
        document.getElementById('wireframe-btn').addEventListener('click', () => this.toggleWireframe());
        
        // Manual rotation buttons
        document.querySelectorAll('.btn-rotation').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const move = e.target.dataset.move;
                this.executeMove(move);
            });
        });
        
        // Camera preset buttons
        document.getElementById('camera-front').addEventListener('click', () => this.setCameraPreset('front'));
        document.getElementById('camera-corner').addEventListener('click', () => this.setCameraPreset('corner'));
        document.getElementById('camera-top').addEventListener('click', () => this.setCameraPreset('top'));
        document.getElementById('camera-side').addEventListener('click', () => this.setCameraPreset('side'));

        // Zoom control buttons
        document.getElementById('zoom-in').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoom-out').addEventListener('click', () => this.zoomOut());
        document.getElementById('zoom-reset').addEventListener('click', () => this.resetZoom());
        
        // Data generation buttons
        document.getElementById('capture-state').addEventListener('click', () => this.captureCurrentState());
        document.getElementById('generate-batch').addEventListener('click', () => this.generateBatch());
        document.getElementById('export-data').addEventListener('click', () => this.exportData());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'w' || e.key === 'W') {
                this.toggleWireframe();
            } else if (e.key === '+' || e.key === '=') {
                this.zoomIn();
            } else if (e.key === '-' || e.key === '_') {
                this.zoomOut();
            } else if (e.key === '0') {
                this.resetZoom();
            }
        });
    }
    
    executeMove(move) {
        if (this.isAnimating) return;

        console.log(`Executing move: ${move}`);
        this.moveHistory.push(move);
        this.updateMovesCount();
        this.updateStatus(`Executing move: ${move}`);

        // Execute the rotation
        this.rotateFace(move);
    }

    rotateFace(move) {
        this.isAnimating = true;

        // Parse move (e.g., "R", "R'", "U", etc.)
        const face = move.charAt(0);
        const isPrime = move.includes("'");

        // Calculate angle based on face and direction
        // All rotations should be clockwise when viewed from the positive axis direction
        let angle;
        switch(face) {
            case 'R':
                angle = isPrime ? Math.PI / 2 : -Math.PI / 2; // Clockwise when viewed from right
                break;
            case 'L':
                angle = isPrime ? -Math.PI / 2 : Math.PI / 2; // Clockwise when viewed from left
                break;
            case 'U':
                angle = isPrime ? Math.PI / 2 : -Math.PI / 2; // Clockwise when viewed from top
                break;
            case 'D':
                angle = isPrime ? -Math.PI / 2 : Math.PI / 2; // Clockwise when viewed from bottom
                break;
            case 'F':
                angle = isPrime ? Math.PI / 2 : -Math.PI / 2; // Clockwise when viewed from front
                break;
            case 'B':
                angle = isPrime ? -Math.PI / 2 : Math.PI / 2; // Clockwise when viewed from back
                break;
            default:
                angle = isPrime ? -Math.PI / 2 : Math.PI / 2;
        }

        // Get cubelets that belong to this face
        const faceGroup = this.getFaceGroup(face);

        // Create a temporary group for rotation
        const rotationGroup = new THREE.Group();
        this.scene.add(rotationGroup);

        // Move cubelets to rotation group
        faceGroup.forEach(cubelet => {
            this.cube.remove(cubelet);
            rotationGroup.add(cubelet);
        });

        // Animate rotation
        this.animateRotation(rotationGroup, move, angle, () => {
            // Move cubelets back to main cube group while preserving their new positions AND rotations
            const cubeletsToMove = [...rotationGroup.children];
            cubeletsToMove.forEach(cubelet => {
                // Get the cubelet's world position and rotation after rotation
                const worldPosition = new THREE.Vector3();
                const worldQuaternion = new THREE.Quaternion();
                const worldScale = new THREE.Vector3();

                cubelet.matrixWorld.decompose(worldPosition, worldQuaternion, worldScale);

                // Remove from rotation group
                rotationGroup.remove(cubelet);

                // Apply the world transform as the new local transform
                cubelet.position.copy(worldPosition);
                cubelet.quaternion.copy(worldQuaternion);
                cubelet.scale.copy(worldScale);

                // Add back to main cube group
                this.cube.add(cubelet);
            });

            this.scene.remove(rotationGroup);
            this.isAnimating = false;
            this.updateStatus('Ready');
        });
    }

    getFaceGroup(face) {
        const faceGroups = {
            'R': this.cubelets.filter(c => c.userData.x === 2), // Right
            'L': this.cubelets.filter(c => c.userData.x === 0), // Left
            'U': this.cubelets.filter(c => c.userData.y === 2), // Up
            'D': this.cubelets.filter(c => c.userData.y === 0), // Down
            'F': this.cubelets.filter(c => c.userData.z === 2), // Front
            'B': this.cubelets.filter(c => c.userData.z === 0)  // Back
        };

        return faceGroups[face] || [];
    }

    animateRotation(group, move, targetAngle, onComplete) {
        const startTime = Date.now();
        const duration = 300; // 300ms animation

        // Determine rotation axis
        const face = move.charAt(0);
        const axis = this.getRotationAxis(face);

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const eased = this.easeInOutCubic(progress);
            const currentAngle = targetAngle * eased;

            // Reset rotation and apply new rotation
            group.rotation.set(0, 0, 0);
            group.rotateOnAxis(axis, currentAngle);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Finalize positions
                this.updateCubeletPositions(group);
                onComplete();
            }
        };

        animate();
    }

    getRotationAxis(face) {
        const axes = {
            'R': new THREE.Vector3(1, 0, 0),   // X-axis (positive)
            'L': new THREE.Vector3(1, 0, 0),   // X-axis (same as R, direction handled by angle)
            'U': new THREE.Vector3(0, 1, 0),   // Y-axis (positive)
            'D': new THREE.Vector3(0, 1, 0),   // Y-axis (same as U, direction handled by angle)
            'F': new THREE.Vector3(0, 0, 1),   // Z-axis (positive)
            'B': new THREE.Vector3(0, 0, 1)    // Z-axis (same as F, direction handled by angle)
        };

        return axes[face] || new THREE.Vector3(0, 1, 0);
    }

    updateCubeletPositions(group) {
        const positionMap = new Map();

        // Update the userData positions after rotation
        group.children.forEach(cubelet => {
            const pos = cubelet.position;

            // Round positions to avoid floating point errors
            pos.x = Math.round(pos.x);
            pos.y = Math.round(pos.y);
            pos.z = Math.round(pos.z);

            // Create position key for validation
            const posKey = `${pos.x},${pos.y},${pos.z}`;

            // Check for position conflicts
            if (positionMap.has(posKey)) {
                console.error(`Position conflict detected at ${posKey}!`);
                console.error('Existing cubelet:', positionMap.get(posKey).userData);
                console.error('New cubelet:', cubelet.userData);
            } else {
                positionMap.set(posKey, cubelet);
            }

            // Update userData indices
            cubelet.userData.x = pos.x + 1; // Convert from -1,0,1 to 0,1,2
            cubelet.userData.y = pos.y + 1;
            cubelet.userData.z = pos.z + 1;

            console.log(`Cubelet moved to position [${cubelet.userData.x},${cubelet.userData.y},${cubelet.userData.z}]`);
        });

        // Validate total cube integrity
        this.validateCubeIntegrity();
    }

    validateCubeIntegrity() {
        const positions = new Set();
        let duplicates = 0;

        this.cubelets.forEach(cubelet => {
            const posKey = `${cubelet.userData.x},${cubelet.userData.y},${cubelet.userData.z}`;
            if (positions.has(posKey)) {
                duplicates++;
                console.error(`Duplicate position found: ${posKey}`);
            } else {
                positions.add(posKey);
            }
        });

        if (duplicates > 0) {
            console.error(`CUBE INTEGRITY VIOLATION: ${duplicates} duplicate positions found!`);
        } else if (positions.size !== 27) {
            console.error(`CUBE INTEGRITY VIOLATION: Expected 27 positions, found ${positions.size}!`);
        } else {
            console.log('Cube integrity OK: All 27 positions unique');
        }
    }

    // Color rotation functions removed - physical rotation handles color changes

    // All color rotation logic removed - physical rotation handles this automatically
    
    scramble() {
        if (this.isAnimating) return;

        const moves = ['R', 'L', 'U', 'D', 'F', 'B', "R'", "L'", "U'", "D'", "F'", "B'"];
        const scrambleLength = 20;
        const scrambleMoves = [];

        // Generate random moves
        for (let i = 0; i < scrambleLength; i++) {
            const randomMove = moves[Math.floor(Math.random() * moves.length)];
            scrambleMoves.push(randomMove);
        }

        this.updateStatus('Scrambling...');
        this.executeMovesSequence(scrambleMoves, () => {
            this.updateStatus('Scrambled!');
        });
    }

    executeMovesSequence(moves, onComplete) {
        if (moves.length === 0) {
            if (onComplete) onComplete();
            return;
        }

        const move = moves.shift();
        this.moveHistory.push(move);
        this.updateMovesCount();

        // Execute move without updating status (to avoid spam)
        this.rotateFaceQuiet(move, () => {
            // Continue with next move
            setTimeout(() => {
                this.executeMovesSequence(moves, onComplete);
            }, 50); // Small delay between moves
        });
    }

    rotateFaceQuiet(move, onComplete) {
        this.isAnimating = true;

        // Parse move
        const face = move.charAt(0);
        const isPrime = move.includes("'");

        // Calculate angle based on face and direction
        let angle;
        switch(face) {
            case 'R':
                angle = isPrime ? Math.PI / 2 : -Math.PI / 2; // Clockwise when viewed from right
                break;
            case 'L':
                angle = isPrime ? -Math.PI / 2 : Math.PI / 2; // Clockwise when viewed from left
                break;
            case 'U':
                angle = isPrime ? Math.PI / 2 : -Math.PI / 2; // Clockwise when viewed from top
                break;
            case 'D':
                angle = isPrime ? -Math.PI / 2 : Math.PI / 2; // Clockwise when viewed from bottom
                break;
            case 'F':
                angle = isPrime ? Math.PI / 2 : -Math.PI / 2; // Clockwise when viewed from front
                break;
            case 'B':
                angle = isPrime ? -Math.PI / 2 : Math.PI / 2; // Clockwise when viewed from back
                break;
            default:
                angle = isPrime ? -Math.PI / 2 : Math.PI / 2;
        }

        // Get cubelets that belong to this face
        const faceGroup = this.getFaceGroup(face);

        // Create a temporary group for rotation
        const rotationGroup = new THREE.Group();
        this.scene.add(rotationGroup);

        // Move cubelets to rotation group
        faceGroup.forEach(cubelet => {
            this.cube.remove(cubelet);
            rotationGroup.add(cubelet);
        });

        // Animate rotation (faster for scrambling)
        this.animateRotationFast(rotationGroup, move, angle, () => {
            // Move cubelets back to main cube group while preserving their new positions AND rotations
            const cubeletsToMove = [...rotationGroup.children];
            cubeletsToMove.forEach(cubelet => {
                // Get the cubelet's world position and rotation after rotation
                const worldPosition = new THREE.Vector3();
                const worldQuaternion = new THREE.Quaternion();
                const worldScale = new THREE.Vector3();

                cubelet.matrixWorld.decompose(worldPosition, worldQuaternion, worldScale);

                // Remove from rotation group
                rotationGroup.remove(cubelet);

                // Apply the world transform as the new local transform
                cubelet.position.copy(worldPosition);
                cubelet.quaternion.copy(worldQuaternion);
                cubelet.scale.copy(worldScale);

                // Add back to main cube group
                this.cube.add(cubelet);
            });

            this.scene.remove(rotationGroup);
            this.isAnimating = false;
            if (onComplete) onComplete();
        });
    }

    animateRotationFast(group, move, targetAngle, onComplete) {
        const startTime = Date.now();
        const duration = 150; // Faster animation for scrambling

        // Determine rotation axis
        const face = move.charAt(0);
        const axis = this.getRotationAxis(face);

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const eased = this.easeInOutCubic(progress);
            const currentAngle = targetAngle * eased;

            // Reset rotation and apply new rotation
            group.rotation.set(0, 0, 0);
            group.rotateOnAxis(axis, currentAngle);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Finalize positions
                this.updateCubeletPositions(group);
                onComplete();
            }
        };

        animate();
    }
    
    reset() {
        this.moveHistory = [];
        this.applySolvedColors();
        this.updateMovesCount();
        this.updateStatus('Reset to solved state');
    }
    
    setCameraPreset(preset) {
        const { position, target } = this.cameraPresets[preset];
        
        // Animate camera to new position
        const startPos = this.camera.position.clone();
        const endPos = new THREE.Vector3(...position);
        const startTime = Date.now();
        const duration = 1000; // 1 second
        
        const animateCamera = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const eased = this.easeInOutCubic(progress);
            
            this.camera.position.lerpVectors(startPos, endPos, eased);
            this.controls.target.set(...target);
            this.controls.update();
            
            if (progress < 1) {
                requestAnimationFrame(animateCamera);
            }
        };
        
        animateCamera();
        this.updateCameraInfo(preset);
    }
    
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }
    
    captureCurrentState() {
        const state = this.getCurrentState();
        const screenshot = this.takeScreenshot();
        
        this.capturedStates.push({
            id: Date.now(),
            timestamp: new Date().toISOString(),
            cubeState: state,
            cameraPosition: this.camera.position.toArray(),
            cameraTarget: this.controls.target.toArray(),
            moveHistory: [...this.moveHistory],
            screenshot: screenshot
        });
        
        this.updateCapturedCount();
        this.updateStatus('State captured!');
    }
    
    getCurrentState() {
        const state = {
            faces: {
                front: this.getFaceColors('front'),
                back: this.getFaceColors('back'),
                right: this.getFaceColors('right'),
                left: this.getFaceColors('left'),
                top: this.getFaceColors('top'),
                bottom: this.getFaceColors('bottom')
            }
        };

        return state;
    }

    getFaceColors(faceName) {
        const colors = [];
        const colorMap = {
            [this.colors.white]: 'white',
            [this.colors.yellow]: 'yellow',
            [this.colors.red]: 'red',
            [this.colors.orange]: 'orange',
            [this.colors.blue]: 'blue',
            [this.colors.green]: 'green'
        };

        // Define face mappings based on current cube orientation
        // Standard orientation: Green front, White top, Orange right, Red left, Blue back, Yellow bottom
        const faceMapping = {
            'front': { axis: 'z', value: 2, materialIndex: 4 },   // Green
            'back': { axis: 'z', value: 0, materialIndex: 5 },    // Blue
            'right': { axis: 'x', value: 2, materialIndex: 0 },   // Orange
            'left': { axis: 'x', value: 0, materialIndex: 1 },    // Red
            'top': { axis: 'y', value: 2, materialIndex: 2 },     // White
            'bottom': { axis: 'y', value: 0, materialIndex: 3 }   // Yellow
        };

        const mapping = faceMapping[faceName];
        if (!mapping) return Array(9).fill('unknown');

        // Get cubelets for this face
        const faceCubelets = this.cubelets.filter(cubelet => {
            return cubelet.userData[mapping.axis] === mapping.value;
        });

        // Sort cubelets by position to get consistent ordering
        faceCubelets.sort((a, b) => {
            if (faceName === 'front' || faceName === 'back') {
                // Sort by Y (top to bottom), then X (left to right)
                if (a.userData.y !== b.userData.y) return b.userData.y - a.userData.y;
                return a.userData.x - b.userData.x;
            } else if (faceName === 'top' || faceName === 'bottom') {
                // Sort by Z (back to front), then X (left to right)
                if (a.userData.z !== b.userData.z) return a.userData.z - b.userData.z;
                return a.userData.x - b.userData.x;
            } else { // left or right
                // Sort by Y (top to bottom), then Z (back to front)
                if (a.userData.y !== b.userData.y) return b.userData.y - a.userData.y;
                return a.userData.z - b.userData.z;
            }
        });

        // Extract colors
        faceCubelets.forEach(cubelet => {
            const material = cubelet.material[mapping.materialIndex];
            const colorHex = material.color.getHex();
            const colorName = colorMap[colorHex] || 'unknown';
            colors.push(colorName);
        });

        // Ensure we have exactly 9 colors
        while (colors.length < 9) {
            colors.push('unknown');
        }

        return colors.slice(0, 9);
    }
    
    takeScreenshot() {
        return this.renderer.domElement.toDataURL('image/png');
    }
    
    generateBatch() {
        if (this.isAnimating) return;

        const batchSize = parseInt(document.getElementById('batch-size').value);
        const anglesPerState = parseInt(document.getElementById('angles-per-state').value);

        this.updateStatus(`Generating batch: ${batchSize} states × ${anglesPerState} angles...`);

        // Disable controls during batch generation
        this.setControlsEnabled(false);

        this.generateBatchRecursive(0, batchSize, anglesPerState, () => {
            this.setControlsEnabled(true);
            this.updateStatus(`Batch complete! Generated ${batchSize * anglesPerState} samples.`);
            this.updateCapturedCount();
        });
    }

    generateBatchRecursive(currentState, totalStates, anglesPerState, onComplete) {
        if (currentState >= totalStates) {
            onComplete();
            return;
        }

        // Reset cube and scramble
        this.reset();

        setTimeout(() => {
            // Generate random scramble
            const moves = ['R', 'L', 'U', 'D', 'F', 'B', "R'", "L'", "U'", "D'", "F'", "B'"];
            const scrambleLength = 15 + Math.floor(Math.random() * 10); // 15-25 moves
            const scrambleMoves = [];

            for (let i = 0; i < scrambleLength; i++) {
                const randomMove = moves[Math.floor(Math.random() * moves.length)];
                scrambleMoves.push(randomMove);
            }

            // Execute scramble quickly
            this.executeMovesSequence([...scrambleMoves], () => {
                // Now capture from multiple angles
                this.captureMultipleAngles(0, anglesPerState, () => {
                    // Move to next state
                    this.generateBatchRecursive(currentState + 1, totalStates, anglesPerState, onComplete);
                });
            });
        }, 100);
    }

    captureMultipleAngles(currentAngle, totalAngles, onComplete) {
        if (currentAngle >= totalAngles) {
            onComplete();
            return;
        }

        // Set random camera position
        this.setRandomCameraPosition(() => {
            // Capture state
            this.captureCurrentState();

            // Move to next angle
            setTimeout(() => {
                this.captureMultipleAngles(currentAngle + 1, totalAngles, onComplete);
            }, 200); // Small delay for camera animation
        });
    }

    setRandomCameraPosition(onComplete) {
        // Generate random spherical coordinates
        const radius = 6 + Math.random() * 4; // 6-10 units from center
        const theta = Math.random() * Math.PI * 2; // 0-360 degrees
        const phi = Math.PI * 0.2 + Math.random() * Math.PI * 0.6; // 36-144 degrees (avoid top/bottom extremes)

        const x = radius * Math.sin(phi) * Math.cos(theta);
        const y = radius * Math.cos(phi);
        const z = radius * Math.sin(phi) * Math.sin(theta);

        // Animate camera to new position
        const startPos = this.camera.position.clone();
        const endPos = new THREE.Vector3(x, y, z);
        const startTime = Date.now();
        const duration = 500; // 0.5 second

        const animateCamera = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const eased = this.easeInOutCubic(progress);

            this.camera.position.lerpVectors(startPos, endPos, eased);
            this.controls.target.set(0, 0, 0);
            this.controls.update();

            if (progress < 1) {
                requestAnimationFrame(animateCamera);
            } else {
                if (onComplete) onComplete();
            }
        };

        animateCamera();
    }

    setControlsEnabled(enabled) {
        const buttons = document.querySelectorAll('.btn');
        const inputs = document.querySelectorAll('input');

        buttons.forEach(btn => btn.disabled = !enabled);
        inputs.forEach(input => input.disabled = !enabled);
    }

    toggleWireframe() {
        this.wireframeMode = !this.wireframeMode;

        this.cubelets.forEach(cubelet => {
            if (this.wireframeMode) {
                // Switch to debug mode - smaller cubes with thick edges
                cubelet.scale.set(0.7, 0.7, 0.7); // Make cubes smaller

                cubelet.material.forEach(material => {
                    material.wireframe = false; // Keep solid faces
                    material.transparent = true;
                    material.opacity = 0.8; // Semi-transparent
                });

                // Make edge lines much more visible
                if (cubelet.userData.edgeLines) {
                    cubelet.userData.edgeLines.material.color.setHex(0xffffff); // White edges
                    cubelet.userData.edgeLines.material.linewidth = 8; // Very thick lines
                    cubelet.userData.edgeLines.scale.set(1.43, 1.43, 1.43); // Scale edges back up
                }
            } else {
                // Switch back to normal mode
                cubelet.scale.set(1, 1, 1); // Normal size

                cubelet.material.forEach(material => {
                    material.wireframe = false;
                    material.transparent = false;
                    material.opacity = 1.0;
                });

                // Reset edge lines to normal
                if (cubelet.userData.edgeLines) {
                    cubelet.userData.edgeLines.material.color.setHex(0x000000); // Black edges
                    cubelet.userData.edgeLines.material.linewidth = 3; // Normal thickness
                    cubelet.userData.edgeLines.scale.set(1, 1, 1); // Normal scale
                }
            }
        });

        // Update status
        this.updateStatus(this.wireframeMode ? 'Debug Mode ON - Small cubes with thick edges (Press W to toggle)' : 'Debug Mode OFF');

        // Update background for better visibility
        if (this.wireframeMode) {
            this.scene.background = new THREE.Color(0x000000); // Black background
        } else {
            this.scene.background = new THREE.Color(0x222222); // Original background
        }
    }

    // Debug method to log cubelet colors
    debugCubeletColors(cubelet, label = '') {
        const { x, y, z } = cubelet.userData;
        const colors = cubelet.material.map(mat => {
            const hex = mat.color.getHex();
            const colorName = this.getColorName(hex);
            return `${colorName}(${hex.toString(16)})`;
        });

        console.log(`${label} Cubelet[${x},${y},${z}]:`, {
            right: colors[0],
            left: colors[1],
            top: colors[2],
            bottom: colors[3],
            front: colors[4],
            back: colors[5]
        });
    }

    getColorName(hex) {
        const colorMap = {
            [this.colors.white]: 'white',
            [this.colors.yellow]: 'yellow',
            [this.colors.red]: 'red',
            [this.colors.orange]: 'orange',
            [this.colors.blue]: 'blue',
            [this.colors.green]: 'green',
            0x333333: 'dark'
        };

        // Debug: log the hex value to see what we're getting
        const colorName = colorMap[hex];
        if (!colorName && hex !== 0x333333) {
            console.log(`Unknown color hex: ${hex.toString(16)} (${hex})`);
        }

        return colorName || `unknown(${hex.toString(16)})`;
    }
    
    exportData() {
        if (this.capturedStates.length === 0) {
            alert('No data to export. Capture some states first!');
            return;
        }
        
        const data = {
            metadata: {
                exportDate: new Date().toISOString(),
                totalStates: this.capturedStates.length,
                version: '1.0'
            },
            states: this.capturedStates
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `rubiks_cube_data_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.updateStatus('Data exported!');
    }
    
    // UI Update Methods
    updateMovesCount() {
        document.getElementById('moves-count').textContent = this.moveHistory.length;
    }
    
    updateCapturedCount() {
        document.getElementById('captured-count').textContent = this.capturedStates.length;
    }
    
    updateCameraInfo(preset) {
        document.getElementById('camera-position').textContent = `Camera: ${preset}`;
        document.getElementById('current-camera').textContent = preset;
    }

    zoomIn() {
        const currentDistance = this.camera.position.distanceTo(this.controls.target);
        const newDistance = Math.max(currentDistance * 0.8, 2); // Minimum distance of 2
        this.zoomToDistance(newDistance);
    }

    zoomOut() {
        const currentDistance = this.camera.position.distanceTo(this.controls.target);
        const newDistance = Math.min(currentDistance * 1.25, 20); // Maximum distance of 20
        this.zoomToDistance(newDistance);
    }

    resetZoom() {
        this.zoomToDistance(8); // Default distance
    }

    zoomToDistance(targetDistance) {
        const currentPos = this.camera.position.clone();
        const target = this.controls.target.clone();
        const direction = currentPos.sub(target).normalize();
        const newPosition = target.clone().add(direction.multiplyScalar(targetDistance));

        // Animate zoom
        const startPos = this.camera.position.clone();
        const startTime = Date.now();
        const duration = 300;

        const animateZoom = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const eased = this.easeInOutCubic(progress);

            this.camera.position.lerpVectors(startPos, newPosition, eased);
            this.controls.update();

            if (progress < 1) {
                requestAnimationFrame(animateZoom);
            }
        };

        animateZoom();
        this.updateStatus(`Zoom: ${targetDistance.toFixed(1)} units`);
    }
    
    updateStatus(message) {
        document.getElementById('status-text').textContent = message;
    }
    
    onWindowResize() {
        const canvas = document.getElementById('cube-canvas');
        this.camera.aspect = canvas.clientWidth / canvas.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
        
        // Update FPS counter
        this.updateFPS();
    }
    
    updateFPS() {
        // Simple FPS counter
        if (!this.lastTime) this.lastTime = Date.now();
        if (!this.frameCount) this.frameCount = 0;
        
        this.frameCount++;
        const now = Date.now();
        
        if (now - this.lastTime >= 1000) {
            const fps = Math.round((this.frameCount * 1000) / (now - this.lastTime));
            document.getElementById('performance-info').textContent = `FPS: ${fps}`;
            this.frameCount = 0;
            this.lastTime = now;
        }
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.rubiksCube = new RubiksCube();
});
